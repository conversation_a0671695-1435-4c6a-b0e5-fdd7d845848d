// Validation utilities

const validateChatRequest = (req, res, next) => {
  const { model, messages, options = {} } = req.body;

  // Check if model is provided
  if (!model || typeof model !== 'string' || model.trim() === '') {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Model name is required and must be a non-empty string',
        field: 'model',
        timestamp: new Date().toISOString()
      }
    });
  }

  // Check if messages is provided and is an array
  if (!messages || !Array.isArray(messages)) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Messages must be an array',
        field: 'messages',
        timestamp: new Date().toISOString()
      }
    });
  }

  // Check if messages array is not empty
  if (messages.length === 0) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Messages array cannot be empty',
        field: 'messages',
        timestamp: new Date().toISOString()
      }
    });
  }

  // Validate each message structure
  for (let i = 0; i < messages.length; i++) {
    const message = messages[i];
    
    if (!message || typeof message !== 'object') {
      return res.status(400).json({
        success: false,
        error: {
          message: `Message at index ${i} must be an object`,
          field: `messages[${i}]`,
          timestamp: new Date().toISOString()
        }
      });
    }

    if (!message.role || typeof message.role !== 'string') {
      return res.status(400).json({
        success: false,
        error: {
          message: `Message at index ${i} must have a valid role`,
          field: `messages[${i}].role`,
          timestamp: new Date().toISOString()
        }
      });
    }

    if (!message.content || typeof message.content !== 'string') {
      return res.status(400).json({
        success: false,
        error: {
          message: `Message at index ${i} must have valid content`,
          field: `messages[${i}].content`,
          timestamp: new Date().toISOString()
        }
      });
    }

    // Validate role values
    const validRoles = ['system', 'user', 'assistant'];
    if (!validRoles.includes(message.role)) {
      return res.status(400).json({
        success: false,
        error: {
          message: `Message at index ${i} has invalid role. Must be one of: ${validRoles.join(', ')}`,
          field: `messages[${i}].role`,
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Validate options if provided
  if (options && typeof options === 'object') {
    const numericOptions = ['temperature', 'top_p', 'top_k', 'repeat_penalty', 'seed'];
    
    for (const option of numericOptions) {
      if (options[option] !== undefined) {
        const value = options[option];
        if (typeof value !== 'number' || isNaN(value)) {
          return res.status(400).json({
            success: false,
            error: {
              message: `Option '${option}' must be a valid number`,
              field: `options.${option}`,
              timestamp: new Date().toISOString()
            }
          });
        }

        // Specific range validations
        if (option === 'temperature' && (value < 0 || value > 2)) {
          return res.status(400).json({
            success: false,
            error: {
              message: 'Temperature must be between 0 and 2',
              field: 'options.temperature',
              timestamp: new Date().toISOString()
            }
          });
        }

        if ((option === 'top_p') && (value < 0 || value > 1)) {
          return res.status(400).json({
            success: false,
            error: {
              message: 'top_p must be between 0 and 1',
              field: 'options.top_p',
              timestamp: new Date().toISOString()
            }
          });
        }

        if (option === 'top_k' && value < 1) {
          return res.status(400).json({
            success: false,
            error: {
              message: 'top_k must be at least 1',
              field: 'options.top_k',
              timestamp: new Date().toISOString()
            }
          });
        }
      }
    }
  }

  next();
};

const validateModelName = (req, res, next) => {
  const modelName = req.body.name || req.params.name;

  if (!modelName || typeof modelName !== 'string' || modelName.trim() === '') {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Model name is required and must be a non-empty string',
        field: 'name',
        timestamp: new Date().toISOString()
      }
    });
  }

  // Basic model name validation (alphanumeric, hyphens, underscores, colons, slashes)
  const modelNameRegex = /^[a-zA-Z0-9\-_:\/\.]+$/;
  if (!modelNameRegex.test(modelName)) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Model name contains invalid characters. Only alphanumeric characters, hyphens, underscores, colons, slashes, and dots are allowed',
        field: 'name',
        timestamp: new Date().toISOString()
      }
    });
  }

  next();
};

const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

module.exports = {
  validateChatRequest,
  validateModelName,
  asyncHandler
};
