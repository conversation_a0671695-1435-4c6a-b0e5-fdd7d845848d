const express = require('express');
const cors = require('cors');
const axios = require('axios');
const helmet = require('helmet');
const morgan = require('morgan');
const { validateChatRequest, validateModelName, asyncHandler } = require('./utils/validation');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;
const OLLAMA_BASE_URL = process.env.OLLAMA_BASE_URL || 'http://localhost:11434';

// Middleware
app.use(helmet());
app.use(morgan('combined'));
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : '*',
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    service: 'Ollama Backend API'
  });
});

// API Routes

// Get available models
app.get('/api/models', async (req, res) => {
  try {
    const response = await axios.get(`${OLLAMA_BASE_URL}/api/tags`);

    const models = response.data.models || [];
    const formattedModels = models.map(model => ({
      name: model.name,
      size: model.size,
      digest: model.digest,
      modified_at: model.modified_at,
      details: model.details || {}
    }));

    res.json({
      success: true,
      models: formattedModels,
      count: formattedModels.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error fetching models:', error.message);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch models from Ollama',
        details: error.response?.data || error.message,
        timestamp: new Date().toISOString()
      }
    });
  }
});

// Chat completion endpoint
app.post('/api/chat', validateChatRequest, asyncHandler(async (req, res) => {
  const { model, messages, stream = false, options = {} } = req.body;

  const requestData = {
    model,
    messages,
    stream,
    options: {
      temperature: options.temperature || 0.7,
      top_p: options.top_p || 0.9,
      top_k: options.top_k || 40,
      ...options
    }
  };

  if (stream) {
    // Streaming response
    res.setHeader('Content-Type', 'text/plain');
    res.setHeader('Transfer-Encoding', 'chunked');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');

    const response = await axios.post(`${OLLAMA_BASE_URL}/api/chat`, requestData, {
      responseType: 'stream'
    });

    response.data.on('data', (chunk) => {
      const lines = chunk.toString().split('\n').filter(line => line.trim());

      for (const line of lines) {
        try {
          const data = JSON.parse(line);
          res.write(JSON.stringify(data) + '\n');
        } catch (e) {
          // Skip invalid JSON lines
        }
      }
    });

    response.data.on('end', () => {
      res.end();
    });

    response.data.on('error', (error) => {
      console.error('Streaming error:', error);
      res.write(JSON.stringify({
        error: 'Stream error occurred',
        timestamp: new Date().toISOString()
      }) + '\n');
      res.end();
    });

  } else {
    // Non-streaming response
    const response = await axios.post(`${OLLAMA_BASE_URL}/api/chat`, requestData);

    res.json({
      success: true,
      message: response.data.message,
      model: response.data.model,
      created_at: response.data.created_at,
      done: response.data.done,
      total_duration: response.data.total_duration,
      load_duration: response.data.load_duration,
      prompt_eval_count: response.data.prompt_eval_count,
      prompt_eval_duration: response.data.prompt_eval_duration,
      eval_count: response.data.eval_count,
      eval_duration: response.data.eval_duration,
      timestamp: new Date().toISOString()
    });
  }
    console.error('Chat completion error:', error.message);

    if (!res.headersSent) {
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to process chat completion',
          details: error.response?.data || error.message,
          timestamp: new Date().toISOString()
        }
      });
    }
}));

// Pull/download a model
app.post('/api/models/pull', validateModelName, asyncHandler(async (req, res) => {
  const { name, stream = false } = req.body;

  const requestData = { name, stream };

  if (stream) {
    // Streaming response for pull progress
    res.setHeader('Content-Type', 'text/plain');
    res.setHeader('Transfer-Encoding', 'chunked');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');

    const response = await axios.post(`${OLLAMA_BASE_URL}/api/pull`, requestData, {
      responseType: 'stream'
    });

    response.data.on('data', (chunk) => {
      const lines = chunk.toString().split('\n').filter(line => line.trim());

      for (const line of lines) {
        try {
          const data = JSON.parse(line);
          res.write(JSON.stringify(data) + '\n');
        } catch (e) {
          // Skip invalid JSON lines
        }
      }
    });

    response.data.on('end', () => {
      res.end();
    });

    response.data.on('error', (error) => {
      console.error('Pull streaming error:', error);
      res.write(JSON.stringify({
        error: 'Pull stream error occurred',
        timestamp: new Date().toISOString()
      }) + '\n');
      res.end();
    });

  } else {
    // Non-streaming response
    const response = await axios.post(`${OLLAMA_BASE_URL}/api/pull`, requestData);

    res.json({
      success: true,
      status: response.data.status,
      timestamp: new Date().toISOString()
    });
  }
    console.error('Model pull error:', error.message);

    if (!res.headersSent) {
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to pull model',
          details: error.response?.data || error.message,
          timestamp: new Date().toISOString()
        }
      });
    }
}));

// Delete a model
app.delete('/api/models/:name', validateModelName, asyncHandler(async (req, res) => {
  const { name } = req.params;

  await axios.delete(`${OLLAMA_BASE_URL}/api/delete`, {
    data: { name }
  });

  res.json({
    success: true,
    message: `Model ${name} deleted successfully`,
    timestamp: new Date().toISOString()
  });
}));

// Check if model exists
app.get('/api/models/:name/exists', validateModelName, asyncHandler(async (req, res) => {
  const { name } = req.params;

  const response = await axios.get(`${OLLAMA_BASE_URL}/api/tags`);
  const models = response.data.models || [];
  const modelExists = models.some(model => model.name === name);

  res.json({
    success: true,
    exists: modelExists,
    model: name,
    timestamp: new Date().toISOString()
  });
}));

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(err.status || 500).json({
    error: {
      message: err.message || 'Internal Server Error',
      status: err.status || 500,
      timestamp: new Date().toISOString()
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: {
      message: 'Endpoint not found',
      status: 404,
      timestamp: new Date().toISOString()
    }
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Ollama Backend API running on port ${PORT}`);
  console.log(`📡 Ollama URL: ${OLLAMA_BASE_URL}`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});
