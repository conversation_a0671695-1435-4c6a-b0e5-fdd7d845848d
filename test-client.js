#!/usr/bin/env node

// Simple test client for the Ollama Backend API
const axios = require('axios');

const BASE_URL = 'http://localhost:8080';

async function testHealthCheck() {
  console.log('🔍 Testing health check...');
  try {
    const response = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health check passed:', response.data);
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
  }
}

async function testListModels() {
  console.log('\n📋 Testing list models...');
  try {
    const response = await axios.get(`${BASE_URL}/api/models`);
    console.log('✅ Models retrieved:', response.data.count, 'models found');
    console.log('Available models:', response.data.models.map(m => m.name).join(', '));
  } catch (error) {
    console.error('❌ List models failed:', error.message);
  }
}

async function testChatCompletion() {
  console.log('\n💬 Testing chat completion...');
  try {
    const response = await axios.post(`${BASE_URL}/api/chat`, {
      model: 'qwen3:1.7b', // Using the first available model
      messages: [
        {
          role: 'user',
          content: 'Hello! Can you tell me a short joke?'
        }
      ],
      stream: false,
      options: {
        temperature: 0.7
      }
    });
    
    console.log('✅ Chat completion successful!');
    console.log('Response:', response.data.message.content);
  } catch (error) {
    console.error('❌ Chat completion failed:', error.response?.data || error.message);
  }
}

async function testModelExists() {
  console.log('\n🔍 Testing model existence check...');
  try {
    const response = await axios.get(`${BASE_URL}/api/models/qwen3:1.7b/exists`);
    console.log('✅ Model existence check:', response.data);
  } catch (error) {
    console.error('❌ Model existence check failed:', error.message);
  }
}

async function runTests() {
  console.log('🚀 Starting Ollama Backend API Tests\n');
  
  await testHealthCheck();
  await testListModels();
  await testModelExists();
  await testChatCompletion();
  
  console.log('\n✨ Tests completed!');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testHealthCheck,
  testListModels,
  testChatCompletion,
  testModelExists,
  runTests
};
