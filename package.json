{"name": "ollama_backend", "version": "1.0.0", "description": "Backend API server for Ollama models with REST endpoints for chat completions and model management", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["ollama", "api", "backend", "ai", "llm", "chat"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.11.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.21.2", "helmet": "^8.1.0", "morgan": "^1.10.1"}, "devDependencies": {"nodemon": "^3.1.10"}}