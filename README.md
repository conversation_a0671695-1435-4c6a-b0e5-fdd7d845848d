# Ollama Backend API

A robust backend API server that provides REST endpoints for interacting with Ollama models. This server acts as a bridge between client applications and Ollama, offering features like chat completions, model management, and streaming support.

## Features

- 🚀 **Chat Completions**: Support for both streaming and non-streaming chat completions
- 📦 **Model Management**: Pull, delete, and check model availability
- 🔒 **Security**: Built-in CORS, Helmet security headers, and input validation
- 📝 **Logging**: Request logging with Morgan
- ⚡ **Performance**: Async/await with proper error handling
- 🌐 **CORS Support**: Configurable cross-origin resource sharing
- 📊 **Health Checks**: Built-in health check endpoint

## Prerequisites

- Node.js (v14 or higher)
- Ollama installed and running on your system
- npm or yarn package manager

## Installation

1. Clone or download this repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Copy the environment configuration:
   ```bash
   cp .env.example .env
   ```

4. Configure your environment variables in `.env`:
   ```env
   PORT=3001
   NODE_ENV=development
   OLLAMA_BASE_URL=http://localhost:11434
   ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
   ```

## Usage

### Development Mode
```bash
npm run dev
```

### Production Mode
```bash
npm start
```

The server will start on the configured port (default: 8080).

## API Endpoints

### Health Check
- **GET** `/health` - Check server status

### Models
- **GET** `/api/models` - List available models
- **POST** `/api/models/pull` - Pull/download a model
- **DELETE** `/api/models/:name` - Delete a model
- **GET** `/api/models/:name/exists` - Check if model exists

### Chat
- **POST** `/api/chat` - Chat completion (streaming and non-streaming)

## API Documentation

### Chat Completion

**Endpoint:** `POST /api/chat`

**Request Body:**
```json
{
  "model": "llama2",
  "messages": [
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ],
  "stream": false,
  "options": {
    "temperature": 0.7,
    "top_p": 0.9,
    "top_k": 40
  }
}
```

**Response (Non-streaming):**
```json
{
  "success": true,
  "message": {
    "role": "assistant",
    "content": "Hello! I'm doing well, thank you for asking..."
  },
  "model": "llama2",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Model Management

**Pull Model:** `POST /api/models/pull`
```json
{
  "name": "llama2",
  "stream": false
}
```

**List Models:** `GET /api/models`

**Delete Model:** `DELETE /api/models/llama2`

**Check Model:** `GET /api/models/llama2/exists`

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Server port | `8080` |
| `NODE_ENV` | Environment mode | `development` |
| `OLLAMA_BASE_URL` | Ollama server URL | `http://localhost:11434` |
| `ALLOWED_ORIGINS` | CORS allowed origins | `*` |

### CORS Configuration

Configure allowed origins in the `.env` file:
```env
# Allow specific origins
ALLOWED_ORIGINS=http://localhost:3000,https://myapp.com

# Allow all origins (not recommended for production)
ALLOWED_ORIGINS=*
```

## Error Handling

The API provides comprehensive error handling with structured error responses:

```json
{
  "success": false,
  "error": {
    "message": "Error description",
    "status": 400,
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

## Security Features

- **Helmet**: Security headers
- **CORS**: Cross-origin resource sharing
- **Input Validation**: Request validation middleware
- **Rate Limiting**: Built-in protection against abuse
- **Error Sanitization**: Prevents sensitive information leakage

## Development

### Project Structure
```
ollama_backend/
├── server.js           # Main server file
├── utils/
│   └── validation.js   # Validation utilities
├── package.json        # Dependencies and scripts
├── .env.example       # Environment template
└── README.md          # Documentation
```

### Adding New Endpoints

1. Create validation middleware in `utils/validation.js`
2. Add route handlers in `server.js`
3. Use `asyncHandler` for async routes
4. Update this documentation

## Troubleshooting

### Common Issues

1. **Ollama not running**: Ensure Ollama is installed and running on the configured URL
2. **CORS errors**: Check `ALLOWED_ORIGINS` configuration
3. **Port conflicts**: Change the `PORT` environment variable
4. **Model not found**: Verify the model is pulled and available in Ollama

### Logs

The server uses Morgan for request logging. Check console output for detailed request information.

## License

ISC License

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request
